#!/usr/bin/env tsx

/**
 * Example usage of the Cloudflare MCP Server
 * 
 * This script demonstrates how to use the MCP server to query
 * Cloudflare analytics and security events using Ray IDs.
 */

import { CloudflareMcpServer } from '../src/server.js';
import { CloudflareConfig } from '../src/types/cloudflare.js';

async function exampleUsage() {
  // Configuration - in real usage, these would come from environment variables
  const config: CloudflareConfig = {
    apiToken: process.env.CLOUDFLARE_API_TOKEN || 'your-api-token-here',
    zoneId: process.env.CLOUDFLARE_ZONE_ID || 'your-zone-id-here',
    accountId: process.env.CLOUDFLARE_ACCOUNT_ID,
  };

  console.log('🚀 Starting Cloudflare MCP Server Example');
  console.log('Configuration:', {
    hasApiToken: !!config.apiToken,
    hasZoneId: !!config.zoneId,
    hasAccountId: !!config.accountId,
  });

  try {
    // Create the MCP server
    const server = new CloudflareMcpServer(config);
    
    console.log('✅ MCP Server created successfully');
    console.log('📋 Available tools:');
    console.log('  - lookup_ray_id: Get comprehensive Ray ID information');
    console.log('  - get_security_events: Get security events for a Ray ID');
    console.log('  - get_request_metadata: Get request metadata for a Ray ID');
    console.log('  - get_performance_metrics: Get performance metrics for a Ray ID');
    
    // Example Ray IDs (these are examples - replace with real Ray IDs)
    const exampleRayIds = [
      '8b2c4d6e8f1a2b3c-LAX',
      '1234567890abcdef-DFW',
      'fedcba0987654321-JFK',
    ];

    console.log('\n📝 Example Ray ID formats:');
    exampleRayIds.forEach(rayId => {
      console.log(`  - ${rayId}`);
    });

    console.log('\n🔍 To use this server:');
    console.log('1. Set your environment variables:');
    console.log('   export CLOUDFLARE_API_TOKEN="your-token"');
    console.log('   export CLOUDFLARE_ZONE_ID="your-zone-id"');
    console.log('');
    console.log('2. Start the server:');
    console.log('   npm start');
    console.log('');
    console.log('3. Use MCP tools to query Ray IDs:');
    console.log('   - lookup_ray_id with rayId parameter');
    console.log('   - get_security_events with rayId parameter');
    console.log('   - get_request_metadata with rayId parameter');
    console.log('   - get_performance_metrics with rayId parameter');

    console.log('\n📊 Example tool call:');
    console.log(JSON.stringify({
      tool: 'lookup_ray_id',
      parameters: {
        rayId: '8b2c4d6e8f1a2b3c-LAX',
        zoneId: 'optional-zone-id'
      }
    }, null, 2));

    console.log('\n📋 Example response structure:');
    console.log(JSON.stringify({
      rayId: '8b2c4d6e8f1a2b3c-LAX',
      found: true,
      requestMetadata: {
        timestamp: '2024-01-15T10:30:00Z',
        rayId: '8b2c4d6e8f1a2b3c-LAX',
        method: 'GET',
        uri: '/api/data',
        protocol: 'HTTP/1.1',
        status: 200,
        originIP: '*************',
        userAgent: 'Mozilla/5.0...',
        country: 'US',
        colo: 'LAX'
      },
      securityEvents: [
        {
          timestamp: '2024-01-15T10:30:00Z',
          rayId: '8b2c4d6e8f1a2b3c-LAX',
          action: 'allow',
          source: 'firewall',
          clientIP: '*************',
          country: 'US'
        }
      ],
      performanceMetrics: {
        rayId: '8b2c4d6e8f1a2b3c-LAX',
        originResponseTime: 150,
        edgeResponseBytes: 2048,
        cacheStatus: 'hit'
      }
    }, null, 2));

  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

// Run the example if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  exampleUsage().catch(console.error);
}
