# Claude Desktop Setup Guide

This guide will help you connect the Cloudflare MCP Server to <PERSON>.

## 🚀 Quick Setup (Recommended)

### 1. Automatic Setup Script

Run the automated setup script:

```bash
npm run setup:claude
```

This script will:
- ✅ Check that your MCP server is built
- ✅ Prompt for your Cloudflare API token
- ✅ Optionally ask for your Zone ID
- ✅ Create the Claude Desktop configuration file
- ✅ Place it in the correct location for your OS

### 2. Restart Claude <PERSON>op

After running the setup script, restart <PERSON> to load the new configuration.

### 3. Test the Integration

In Claude Desktop, try asking:
- "Can you look up the Ray ID 8b2c4d6e8f1a2b3c-LAX?"
- "Show me security events for this Ray ID"
- "Get performance metrics for Ray ID 1234567890abcdef-DFW"

## 🔧 Manual Setup

If you prefer to set up manually or the script doesn't work:

### 1. Build the Server

```bash
npm run build
```

### 2. Get Your Cloudflare API Token

1. Go to [Cloudflare Dashboard](https://dash.cloudflare.com/profile/api-tokens)
2. Click "Create Token"
3. Use "Custom token" template
4. Set permissions:
   - Zone:Zone:Read
   - Zone:Analytics:Read
   - Account:Account Analytics:Read (optional)
5. Copy the generated token

### 3. Find Claude Desktop Config Location

**macOS:**
```
~/Library/Application Support/Claude/claude_desktop_config.json
```

**Windows:**
```
%APPDATA%\Claude\claude_desktop_config.json
```

**Linux:**
```
~/.config/Claude/claude_desktop_config.json
```

### 4. Create/Edit Configuration File

Create or edit the `claude_desktop_config.json` file with this content:

```json
{
  "mcpServers": {
    "cloudflare-analytics": {
      "command": "node",
      "args": ["/FULL/PATH/TO/YOUR/cloudflare-mcp/dist/index.js"],
      "env": {
        "CLOUDFLARE_API_TOKEN": "your-actual-api-token-here",
        "CLOUDFLARE_ZONE_ID": "your-zone-id-here"
      }
    }
  }
}
```

**Important:** Replace `/FULL/PATH/TO/YOUR/cloudflare-mcp/dist/index.js` with the actual full path to your built server.

### 5. Restart Claude Desktop

Close and reopen Claude Desktop to load the new configuration.

## 🔍 Available Tools

Once connected, Claude Desktop will have access to these tools:

### `lookup_ray_id`
Get comprehensive information about a Ray ID including request metadata, security events, and performance metrics.

**Example:** "Look up Ray ID 8b2c4d6e8f1a2b3c-LAX"

### `get_security_events`
Get security events (firewall, WAF, bot detection) for a specific Ray ID.

**Example:** "Show me security events for Ray ID 1234567890abcdef-DFW"

### `get_request_metadata`
Get HTTP request metadata for a specific Ray ID.

**Example:** "Get request details for Ray ID fedcba0987654321-JFK"

### `get_performance_metrics`
Get performance metrics for a specific Ray ID.

**Example:** "Show performance metrics for Ray ID 8b2c4d6e8f1a2b3c-LAX"

## 🐛 Troubleshooting

### Server Not Found
- Make sure you've run `npm run build`
- Check that the path in the config file is correct
- Verify the file exists at the specified path

### Authentication Errors
- Verify your API token is correct
- Check that your token has the required permissions:
  - Zone:Zone:Read
  - Zone:Analytics:Read

### No Data Returned
- Ray IDs must be in format: `xxxxxxxxxxxxxxxx-XXX`
- Ray IDs might be too old (Cloudflare retains logs for limited time)
- Try specifying a Zone ID if you have multiple zones

### Claude Desktop Not Loading Tools
- Restart Claude Desktop completely
- Check the configuration file syntax (must be valid JSON)
- Look for error messages in Claude Desktop

## 📝 Example Conversations

Once set up, you can have conversations like:

**You:** "Can you look up the Ray ID 8b2c4d6e8f1a2b3c-LAX and tell me if there were any security events?"

**Claude:** *Uses the lookup_ray_id tool and analyzes the results*

**You:** "Show me just the performance metrics for that same Ray ID"

**Claude:** *Uses the get_performance_metrics tool*

**You:** "What about security events for Ray ID 1234567890abcdef-DFW?"

**Claude:** *Uses the get_security_events tool*

## 🔒 Security Notes

- Your API token is stored in the Claude Desktop configuration file
- The token is only used locally by the MCP server
- Consider using a token with minimal required permissions
- You can revoke the token anytime from the Cloudflare dashboard
