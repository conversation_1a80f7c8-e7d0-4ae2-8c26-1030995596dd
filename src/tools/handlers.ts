import { CloudflareClient, CloudflareApiError } from '../api/cloudflare-client.js';
import {
  LookupRayIdInputSchema,
  SecurityEventResponseSchema,
  RequestMetadataResponseSchema,
  PerformanceMetricsResponseSchema,
  RayIdLookupResponseSchema,
} from './schemas.js';
import {
  SecurityEvent,
  RequestMetadata,
  PerformanceMetrics,
  RayIdResponse,
} from '../types/cloudflare.js';

export class CloudflareToolHandlers {
  constructor(private client: CloudflareClient) {}

  /**
   * Handle lookup_ray_id tool call
   */
  async handleLookupRayId(args: unknown): Promise<RayIdResponse> {
    try {
      // Validate input
      const input = LookupRayIdInputSchema.parse(args);
      
      // Get comprehensive Ray ID information
      const result = await this.client.getRayIdInfo(input.rayId, input.zoneId);
      
      // Validate response
      const validatedResult = RayIdLookupResponseSchema.parse(result);
      
      return validatedResult;
    } catch (error) {
      if (error instanceof CloudflareApiError) {
        throw new Error(`Cloudflare API error: ${error.message}`);
      }
      
      throw new Error(`Failed to lookup Ray ID: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Handle get_security_events tool call
   */
  async handleGetSecurityEvents(args: unknown): Promise<SecurityEvent[]> {
    try {
      // Validate input
      const input = LookupRayIdInputSchema.parse(args);
      
      // Get security events
      const events = await this.client.getSecurityEvents(input.rayId, input.zoneId);
      
      // Validate each event
      const validatedEvents = events.map(event => SecurityEventResponseSchema.parse(event));
      
      return validatedEvents;
    } catch (error) {
      if (error instanceof CloudflareApiError) {
        throw new Error(`Cloudflare API error: ${error.message}`);
      }
      
      throw new Error(`Failed to get security events: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Handle get_request_metadata tool call
   */
  async handleGetRequestMetadata(args: unknown): Promise<RequestMetadata | null> {
    try {
      // Validate input
      const input = LookupRayIdInputSchema.parse(args);
      
      // Get request metadata
      const metadata = await this.client.getRequestMetadata(input.rayId, input.zoneId);
      
      if (!metadata) {
        return null;
      }
      
      // Validate response
      const validatedMetadata = RequestMetadataResponseSchema.parse(metadata);
      
      return validatedMetadata;
    } catch (error) {
      if (error instanceof CloudflareApiError) {
        throw new Error(`Cloudflare API error: ${error.message}`);
      }
      
      throw new Error(`Failed to get request metadata: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Handle get_performance_metrics tool call
   */
  async handleGetPerformanceMetrics(args: unknown): Promise<PerformanceMetrics | null> {
    try {
      // Validate input
      const input = LookupRayIdInputSchema.parse(args);
      
      // Get performance metrics
      const metrics = await this.client.getPerformanceMetrics(input.rayId, input.zoneId);
      
      if (!metrics) {
        return null;
      }
      
      // Validate response
      const validatedMetrics = PerformanceMetricsResponseSchema.parse(metrics);
      
      return validatedMetrics;
    } catch (error) {
      if (error instanceof CloudflareApiError) {
        throw new Error(`Cloudflare API error: ${error.message}`);
      }
      
      throw new Error(`Failed to get performance metrics: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Route tool calls to appropriate handlers
   */
  async handleToolCall(toolName: string, args: unknown): Promise<any> {
    switch (toolName) {
      case 'lookup_ray_id':
        return this.handleLookupRayId(args);
      
      case 'get_security_events':
        return this.handleGetSecurityEvents(args);
      
      case 'get_request_metadata':
        return this.handleGetRequestMetadata(args);
      
      case 'get_performance_metrics':
        return this.handleGetPerformanceMetrics(args);
      
      default:
        throw new Error(`Unknown tool: ${toolName}`);
    }
  }
}
