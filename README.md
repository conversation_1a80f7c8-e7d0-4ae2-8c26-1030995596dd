# Cloudflare MCP Server

A Model Context Protocol (MCP) server that provides tools to query Cloudflare analytics and security events using Cloudflare Ray IDs.

## Features

- **Ray ID Lookup**: Get comprehensive information about a specific Cloudflare Ray ID
- **Security Events**: Query firewall rules, WAF events, bot detection results, and other security actions
- **Request Metadata**: Retrieve HTTP request details including method, URI, status, client IP, and geographic data
- **Performance Metrics**: Access response times, cache status, and bandwidth usage information
- **Error Handling**: Robust error handling with user-friendly messages and retry logic

## Installation

1. Clone this repository:
```bash
git clone <repository-url>
cd cloudflare-mcp
```

2. Install dependencies:
```bash
npm install
```

3. Build the project:
```bash
npm run build
```

## Configuration

### Environment Variables

Set the following environment variables:

- `CLOUDFLARE_API_TOKEN` (required): Your Cloudflare API token with Analytics:Read permissions
- `CLOUDFLARE_ZONE_ID` (optional): Default zone ID to scope queries
- `CLOUDFLARE_ACCOUNT_ID` (optional): Your Cloudflare account ID

### Creating a Cloudflare API Token

1. Go to [Cloudflare Dashboard](https://dash.cloudflare.com/profile/api-tokens)
2. Click "Create Token"
3. Use the "Custom token" template
4. Set permissions:
   - Zone:Zone:Read
   - Zone:Analytics:Read
   - Account:Account Analytics:Read (if using account-level queries)
5. Add zone/account restrictions as needed
6. Copy the generated token

## Usage

### Option 1: Claude Desktop Integration (Recommended)

The easiest way to use this MCP server is with Claude Desktop:

#### Automatic Setup
```bash
# Run the setup script
./scripts/setup-claude-desktop.sh
```

#### Manual Setup
1. Build the server:
```bash
npm run build
```

2. Create or edit Claude Desktop's configuration file:
   - **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
   - **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`
   - **Linux**: `~/.config/Claude/claude_desktop_config.json`

3. Add this configuration:
```json
{
  "mcpServers": {
    "cloudflare-analytics": {
      "command": "node",
      "args": ["/path/to/your/cloudflare-mcp/dist/index.js"],
      "env": {
        "CLOUDFLARE_API_TOKEN": "your-api-token-here",
        "CLOUDFLARE_ZONE_ID": "your-zone-id-here"
      }
    }
  }
}
```

4. Restart Claude Desktop

#### Using in Claude Desktop
Once configured, you can ask Claude to use the Cloudflare tools:

- "Can you look up the Ray ID 8b2c4d6e8f1a2b3c-LAX?"
- "Show me security events for Ray ID 1234567890abcdef-DFW"
- "Get performance metrics for this Ray ID: fedcba0987654321-JFK"

### Option 2: Standalone Server

```bash
# Set environment variables
export CLOUDFLARE_API_TOKEN="your-api-token-here"
export CLOUDFLARE_ZONE_ID="your-zone-id-here"  # optional

# Start the server
npm start
```

### Available Tools

#### 1. `lookup_ray_id`
Get comprehensive information for a Ray ID including request metadata, security events, and performance metrics.

**Parameters:**
- `rayId` (required): Cloudflare Ray ID in format `xxxxxxxxxxxxxxxx-XXX`
- `zoneId` (optional): Zone ID to scope the search

**Example:**
```json
{
  "rayId": "8b2c4d6e8f1a2b3c-LAX",
  "zoneId": "023e105f4ecef8ad9ca31a8372d0c353"
}
```

#### 2. `get_security_events`
Get security events (firewall, WAF, bot detection) for a specific Ray ID.

**Parameters:**
- `rayId` (required): Cloudflare Ray ID
- `zoneId` (optional): Zone ID to scope the search

#### 3. `get_request_metadata`
Get HTTP request metadata for a specific Ray ID.

**Parameters:**
- `rayId` (required): Cloudflare Ray ID
- `zoneId` (optional): Zone ID to scope the search

#### 4. `get_performance_metrics`
Get performance metrics for a specific Ray ID.

**Parameters:**
- `rayId` (required): Cloudflare Ray ID
- `zoneId` (optional): Zone ID to scope the search

### Ray ID Format

Ray IDs must be in the format: `xxxxxxxxxxxxxxxx-XXX`
- 16 hexadecimal characters
- Followed by a dash
- Followed by 3 uppercase letters (airport code)

Examples:
- `8b2c4d6e8f1a2b3c-LAX`
- `1234567890abcdef-DFW`
- `fedcba0987654321-JFK`

## Response Format

### Successful Response
```json
{
  "rayId": "8b2c4d6e8f1a2b3c-LAX",
  "found": true,
  "requestMetadata": {
    "timestamp": "2024-01-15T10:30:00Z",
    "rayId": "8b2c4d6e8f1a2b3c-LAX",
    "method": "GET",
    "uri": "/api/data",
    "protocol": "HTTP/1.1",
    "status": 200,
    "originIP": "*************",
    "userAgent": "Mozilla/5.0...",
    "country": "US",
    "colo": "LAX"
  },
  "securityEvents": [
    {
      "timestamp": "2024-01-15T10:30:00Z",
      "rayId": "8b2c4d6e8f1a2b3c-LAX",
      "action": "allow",
      "source": "firewall",
      "clientIP": "*************",
      "country": "US"
    }
  ],
  "performanceMetrics": {
    "rayId": "8b2c4d6e8f1a2b3c-LAX",
    "originResponseTime": 150,
    "edgeResponseBytes": 2048,
    "cacheStatus": "hit"
  }
}
```

### Error Response
```json
{
  "error": "Invalid Ray ID format",
  "type": "validation",
  "retryable": false
}
```

## Development

### Scripts

- `npm run build`: Build the TypeScript project
- `npm run dev`: Run in development mode with hot reload
- `npm run test`: Run tests
- `npm run lint`: Run ESLint
- `npm run format`: Format code with Prettier

### Project Structure

```
src/
├── api/                 # Cloudflare API client
│   └── cloudflare-client.ts
├── tools/              # MCP tool definitions and handlers
│   ├── schemas.ts
│   └── handlers.ts
├── types/              # TypeScript type definitions
│   └── cloudflare.ts
├── utils/              # Utility functions
│   └── validation.ts
├── server.ts           # MCP server implementation
└── index.ts           # Entry point
```

## Troubleshooting

### Common Issues

1. **Invalid API Token**: Ensure your token has the correct permissions
2. **Zone ID Required**: Some queries require a zone ID to be specified
3. **Ray ID Not Found**: The Ray ID might be too old or from a different zone
4. **Rate Limiting**: Cloudflare APIs have rate limits; the server will indicate if errors are retryable

### Debugging

Enable debug logging by setting the environment variable:
```bash
export DEBUG=cloudflare-mcp:*
```

## License

MIT License - see LICENSE file for details.
