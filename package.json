{"name": "cloudflare-mcp-server", "version": "1.0.0", "description": "Model Context Protocol server for querying Cloudflare analytics and security events using Ray IDs", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx src/index.ts", "test": "jest", "test:server": "tsx scripts/test-server.ts", "example": "tsx examples/example-usage.ts", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts"}, "keywords": ["mcp", "cloudflare", "analytics", "security", "ray-id"], "author": "Your Name", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "axios": "^1.6.0", "zod": "^3.22.0"}, "devDependencies": {"@types/jest": "^29.0.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.0.0", "prettier": "^3.0.0", "ts-jest": "^29.0.0", "tsx": "^4.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}}