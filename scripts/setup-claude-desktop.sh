#!/bin/bash

# Setup script for connecting Cloudflare MCP Server to <PERSON>

set -e

echo "🚀 Setting up Cloudflare MCP Server for Claude Des<PERSON>op"
echo "=================================================="

# Get the current directory (where the MCP server is located)
MCP_SERVER_PATH="$(pwd)/dist/index.js"

# Check if the built server exists
if [ ! -f "$MCP_SERVER_PATH" ]; then
    echo "❌ Built server not found at $MCP_SERVER_PATH"
    echo "Please run 'npm run build' first"
    exit 1
fi

echo "✅ Found MCP server at: $MCP_SERVER_PATH"

# Determine the Claude Desktop config directory based on OS
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    CLAUDE_CONFIG_DIR="$HOME/Library/Application Support/Claude"
    CLAUDE_CONFIG_FILE="$CLAUDE_CONFIG_DIR/claude_desktop_config.json"
elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    # Windows
    CLAUDE_CONFIG_DIR="$APPDATA/Claude"
    CLAUDE_CONFIG_FILE="$CLAUDE_CONFIG_DIR/claude_desktop_config.json"
else
    # Linux
    CLAUDE_CONFIG_DIR="$HOME/.config/Claude"
    CLAUDE_CONFIG_FILE="$CLAUDE_CONFIG_DIR/claude_desktop_config.json"
fi

echo "📁 Claude Desktop config directory: $CLAUDE_CONFIG_DIR"

# Create the config directory if it doesn't exist
mkdir -p "$CLAUDE_CONFIG_DIR"

# Prompt for Cloudflare API token
echo ""
echo "🔑 Cloudflare API Configuration"
echo "You need to provide your Cloudflare API token."
echo "Get it from: https://dash.cloudflare.com/profile/api-tokens"
echo ""

read -p "Enter your Cloudflare API Token: " CLOUDFLARE_API_TOKEN

if [ -z "$CLOUDFLARE_API_TOKEN" ]; then
    echo "❌ API token is required"
    exit 1
fi

# Optional: Prompt for Zone ID
echo ""
read -p "Enter your Cloudflare Zone ID (optional, press Enter to skip): " CLOUDFLARE_ZONE_ID

# Create the configuration JSON
echo ""
echo "📝 Creating Claude Desktop configuration..."

# Check if config file already exists
if [ -f "$CLAUDE_CONFIG_FILE" ]; then
    echo "⚠️  Configuration file already exists at: $CLAUDE_CONFIG_FILE"
    echo "Creating backup..."
    cp "$CLAUDE_CONFIG_FILE" "$CLAUDE_CONFIG_FILE.backup.$(date +%Y%m%d_%H%M%S)"
    echo "✅ Backup created"
fi

# Create the configuration
if [ -n "$CLOUDFLARE_ZONE_ID" ]; then
    # With Zone ID
    cat > "$CLAUDE_CONFIG_FILE" << EOF
{
  "mcpServers": {
    "cloudflare-analytics": {
      "command": "node",
      "args": ["$MCP_SERVER_PATH"],
      "env": {
        "CLOUDFLARE_API_TOKEN": "$CLOUDFLARE_API_TOKEN",
        "CLOUDFLARE_ZONE_ID": "$CLOUDFLARE_ZONE_ID"
      }
    }
  }
}
EOF
else
    # Without Zone ID
    cat > "$CLAUDE_CONFIG_FILE" << EOF
{
  "mcpServers": {
    "cloudflare-analytics": {
      "command": "node",
      "args": ["$MCP_SERVER_PATH"],
      "env": {
        "CLOUDFLARE_API_TOKEN": "$CLOUDFLARE_API_TOKEN"
      }
    }
  }
}
EOF
fi

echo "✅ Configuration file created at: $CLAUDE_CONFIG_FILE"

echo ""
echo "🎉 Setup Complete!"
echo "=================="
echo ""
echo "Next steps:"
echo "1. Restart Claude Desktop if it's currently running"
echo "2. Open Claude Desktop"
echo "3. You should now see the Cloudflare MCP tools available"
echo ""
echo "Available tools:"
echo "  - lookup_ray_id: Get comprehensive Ray ID information"
echo "  - get_security_events: Get security events for a Ray ID"
echo "  - get_request_metadata: Get request metadata for a Ray ID"
echo "  - get_performance_metrics: Get performance metrics for a Ray ID"
echo ""
echo "Example usage in Claude Desktop:"
echo "\"Can you look up the Ray ID 8b2c4d6e8f1a2b3c-LAX and show me any security events?\""
echo ""
echo "📋 Configuration file location: $CLAUDE_CONFIG_FILE"
echo "🔧 MCP Server location: $MCP_SERVER_PATH"
