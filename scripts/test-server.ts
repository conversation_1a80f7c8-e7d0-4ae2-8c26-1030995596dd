#!/usr/bin/env tsx

/**
 * Test script to verify the MCP server can start and respond to basic requests
 */

import { CloudflareMcpServer } from '../src/server.js';
import { CloudflareConfig } from '../src/types/cloudflare.js';

async function testServer() {
  console.log('🧪 Testing Cloudflare MCP Server...');

  // Test configuration validation
  console.log('1. Testing configuration validation...');
  
  try {
    // This should fail - no API token
    new CloudflareMcpServer({
      apiToken: '',
    });
    console.log('❌ Configuration validation failed - empty token should be rejected');
    process.exit(1);
  } catch (error) {
    console.log('✅ Configuration validation works - empty token rejected');
  }

  // Test with valid configuration (but fake token)
  console.log('2. Testing server creation with valid config...');
  
  const config: CloudflareConfig = {
    apiToken: 'fake-token-for-testing-1234567890abcdef',
    zoneId: '023e105f4ecef8ad9ca31a8372d0c353',
    accountId: '01a7362d577a6c3019a474fd6f485823',
  };

  try {
    const server = new CloudflareMcpServer(config);
    console.log('✅ Server created successfully with valid config');
    
    // Test that we can access the server methods
    console.log('3. Testing server methods...');
    
    // The server should have the expected methods
    if (typeof server.start === 'function' && typeof server.stop === 'function') {
      console.log('✅ Server has required methods (start, stop)');
    } else {
      console.log('❌ Server missing required methods');
      process.exit(1);
    }

  } catch (error) {
    console.log('❌ Server creation failed:', error);
    process.exit(1);
  }

  // Test Ray ID validation
  console.log('4. Testing Ray ID validation...');
  
  const { validateRayId } = await import('../src/utils/validation.js');
  
  const validRayIds = [
    '8b2c4d6e8f1a2b3c-LAX',
    '1234567890abcdef-DFW',
    'fedcba0987654321-JFK',
  ];

  const invalidRayIds = [
    '8b2c4d6e8f1a2b3c',        // Missing airport code
    '8b2c4d6e8f1a2b3c-lax',    // Lowercase airport code
    'invalid-ray-id',          // Completely invalid
  ];

  // Test valid Ray IDs
  for (const rayId of validRayIds) {
    try {
      validateRayId(rayId);
      console.log(`✅ Valid Ray ID accepted: ${rayId}`);
    } catch (error) {
      console.log(`❌ Valid Ray ID rejected: ${rayId}`);
      process.exit(1);
    }
  }

  // Test invalid Ray IDs
  for (const rayId of invalidRayIds) {
    try {
      validateRayId(rayId);
      console.log(`❌ Invalid Ray ID accepted: ${rayId}`);
      process.exit(1);
    } catch (error) {
      console.log(`✅ Invalid Ray ID rejected: ${rayId}`);
    }
  }

  console.log('\n🎉 All tests passed!');
  console.log('\n📋 Server is ready to use with the following tools:');
  console.log('  - lookup_ray_id');
  console.log('  - get_security_events');
  console.log('  - get_request_metadata');
  console.log('  - get_performance_metrics');
  
  console.log('\n🚀 To start the server:');
  console.log('  1. Set CLOUDFLARE_API_TOKEN environment variable');
  console.log('  2. Optionally set CLOUDFLARE_ZONE_ID and CLOUDFLARE_ACCOUNT_ID');
  console.log('  3. Run: npm start');
}

// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testServer().catch((error) => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });
}
