import { z } from 'zod';
import { CloudflareConfigSchema, RayIdSchema } from '../types/cloudflare.js';
export class ValidationError extends Error {
    field;
    constructor(message, field) {
        super(message);
        this.field = field;
        this.name = 'ValidationError';
    }
}
export class ConfigurationError extends Error {
    constructor(message) {
        super(message);
        this.name = 'ConfigurationError';
    }
}
/**
 * Validate Cloudflare configuration
 */
export function validateCloudflareConfig(config) {
    try {
        CloudflareConfigSchema.parse(config);
    }
    catch (error) {
        if (error instanceof z.ZodError) {
            const issues = error.issues.map(issue => `${issue.path.join('.')}: ${issue.message}`);
            throw new ConfigurationError(`Invalid Cloudflare configuration: ${issues.join(', ')}`);
        }
        throw new ConfigurationError(`Configuration validation failed: ${error}`);
    }
}
/**
 * Validate Ray ID format
 */
export function validateRayId(rayId) {
    try {
        RayIdSchema.parse(rayId);
    }
    catch (error) {
        if (error instanceof z.ZodError) {
            throw new ValidationError('Invalid Ray ID format. Expected format: xxxxxxxxxxxxxxxx-XXX (16 hex characters, dash, 3 uppercase letters)', 'rayId');
        }
        throw new ValidationError(`Ray ID validation failed: ${error}`, 'rayId');
    }
}
/**
 * Validate Zone ID format (if provided)
 */
export function validateZoneId(zoneId) {
    if (zoneId === undefined)
        return;
    // Cloudflare Zone IDs are 32-character hex strings
    const zoneIdRegex = /^[a-f0-9]{32}$/;
    if (!zoneIdRegex.test(zoneId)) {
        throw new ValidationError('Invalid Zone ID format. Expected 32-character hexadecimal string', 'zoneId');
    }
}
/**
 * Sanitize and validate input parameters
 */
export function sanitizeInput(input) {
    const sanitized = {};
    for (const [key, value] of Object.entries(input)) {
        if (value === null || value === undefined) {
            continue;
        }
        if (typeof value === 'string') {
            // Trim whitespace and remove null bytes
            sanitized[key] = value.trim().replace(/\0/g, '');
        }
        else {
            sanitized[key] = value;
        }
    }
    return sanitized;
}
/**
 * Check if error is retryable
 */
export function isRetryableError(error) {
    const retryableMessages = [
        'timeout',
        'network error',
        'connection reset',
        'econnreset',
        'enotfound',
        'rate limit',
        '429',
        '502',
        '503',
        '504',
    ];
    const errorMessage = error.message.toLowerCase();
    return retryableMessages.some(msg => errorMessage.includes(msg));
}
/**
 * Extract meaningful error message from various error types
 */
export function extractErrorMessage(error) {
    if (error instanceof Error) {
        return error.message;
    }
    if (typeof error === 'string') {
        return error;
    }
    if (error && typeof error === 'object' && 'message' in error) {
        return String(error.message);
    }
    return 'Unknown error occurred';
}
/**
 * Create user-friendly error response
 */
export function createErrorResponse(error, context) {
    const message = extractErrorMessage(error);
    let type = 'unknown';
    let retryable = false;
    if (error instanceof ValidationError) {
        type = 'validation';
    }
    else if (error instanceof ConfigurationError) {
        type = 'configuration';
    }
    else if (error instanceof Error) {
        if (error.name === 'CloudflareApiError') {
            type = 'api';
            retryable = isRetryableError(error);
        }
        else if (error.name === 'AxiosError') {
            type = 'network';
            retryable = true;
        }
        else {
            type = 'runtime';
            retryable = isRetryableError(error);
        }
    }
    return {
        error: message,
        type,
        context,
        retryable,
    };
}
